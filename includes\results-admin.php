<?php
/**
 * Generated Results Admin Page
 */

defined('ABSPATH') || exit;

// Handle pagination
$page = max(1, intval($_GET['paged'] ?? 1));
$per_page = 20;
$offset = ($page - 1) * $per_page;

// Get results with pagination
global $wpdb;
$results = $wpdb->get_results($wpdb->prepare("
    SELECT r.*, o.title as overlay_title, o.category, u.display_name as user_name
    FROM {$wpdb->prefix}ai_results r
    LEFT JOIN {$wpdb->prefix}ai_overlays o ON r.overlay_id = o.id
    LEFT JOIN {$wpdb->users} u ON r.user_id = u.ID
    ORDER BY r.created_at DESC
    LIMIT %d OFFSET %d
", $per_page, $offset));

$total_results = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}ai_results");
$total_pages = ceil($total_results / $per_page);

$upload_dir = wp_upload_dir();
?>

<div class="ai-admin-dashboard">
    <div class="ai-admin-header">
        <div class="ai-header-content">
            <div class="ai-brand">
                <svg class="ai-logo" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                    <circle cx="8.5" cy="8.5" r="1.5"/>
                    <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/>
                </svg>
                <div>
                    <h1>Generated Results</h1>
                    <p>Gallery of all AI-generated images</p>
                </div>
            </div>
            <div class="ai-stats-cards">
                <div class="ai-stat-card">
                    <div class="ai-stat-number"><?php echo $total_results; ?></div>
                    <div class="ai-stat-label">Total Results</div>
                </div>
                <div class="ai-stat-card">
                    <div class="ai-stat-number"><?php echo count($results); ?></div>
                    <div class="ai-stat-label">This Page</div>
                </div>
                <div class="ai-stat-card">
                    <div class="ai-stat-number"><?php echo $total_pages; ?></div>
                    <div class="ai-stat-label">Pages</div>
                </div>
            </div>
        </div>
    </div>

    <div class="ai-admin-content">
        <?php if (empty($results)): ?>
            <div class="ai-empty-state">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                    <circle cx="8.5" cy="8.5" r="1.5"/>
                    <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/>
                </svg>
                <h3>No Results Yet</h3>
                <p>Generated images will appear here once users start creating them.</p>
            </div>
        <?php else: ?>
            <div class="ai-results-grid">
                <?php foreach ($results as $result): ?>
                    <div class="ai-result-card" data-result-id="<?php echo $result->id; ?>">
                        <div class="ai-result-image">
                            <img src="<?php echo esc_url($upload_dir['baseurl'] . $result->image_path); ?>" 
                                 alt="Generated result" 
                                 loading="lazy">
                            <div class="ai-result-overlay">
                                <button type="button" class="ai-btn ai-btn-danger ai-delete-result" 
                                        data-result-id="<?php echo $result->id; ?>">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <polyline points="3,6 5,6 21,6"/>
                                        <path d="m19,6v14a2,2 0 0,1-2,2H7a2,2 0 0,1-2-2V6m3,0V4a2,2 0 0,1,2-2h4a2,2 0 0,1,2,2v2"/>
                                    </svg>
                                    Delete
                                </button>
                            </div>
                        </div>
                        <div class="ai-result-info">
                            <div class="ai-result-meta">
                                <strong><?php echo esc_html($result->overlay_title ?: 'Unknown Overlay'); ?></strong>
                                <span class="ai-result-category"><?php echo esc_html($result->category ?: 'general'); ?></span>
                            </div>
                            <div class="ai-result-details">
                                <span class="ai-result-user">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                                        <circle cx="12" cy="7" r="4"/>
                                    </svg>
                                    <?php echo esc_html($result->user_name ?: 'Guest'); ?>
                                </span>
                                <span class="ai-result-date">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <rect x="3" y="4" width="18" height="18" rx="2" ry="2"/>
                                        <line x1="16" y1="2" x2="16" y2="6"/>
                                        <line x1="8" y1="2" x2="8" y2="6"/>
                                        <line x1="3" y1="10" x2="21" y2="10"/>
                                    </svg>
                                    <?php echo date('M j, Y', strtotime($result->created_at)); ?>
                                </span>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <?php if ($total_pages > 1): ?>
                <div class="ai-pagination">
                    <?php
                    $base_url = admin_url('admin.php?page=ai-styled-results');
                    
                    if ($page > 1): ?>
                        <a href="<?php echo esc_url($base_url . '&paged=' . ($page - 1)); ?>" class="ai-btn ai-btn-secondary">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="15,18 9,12 15,6"/>
                            </svg>
                            Previous
                        </a>
                    <?php endif; ?>
                    
                    <span class="ai-pagination-info">
                        Page <?php echo $page; ?> of <?php echo $total_pages; ?>
                    </span>
                    
                    <?php if ($page < $total_pages): ?>
                        <a href="<?php echo esc_url($base_url . '&paged=' . ($page + 1)); ?>" class="ai-btn ai-btn-secondary">
                            Next
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="9,18 15,12 9,6"/>
                            </svg>
                        </a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle result deletion
    document.addEventListener('click', function(e) {
        if (e.target.closest('.ai-delete-result')) {
            const button = e.target.closest('.ai-delete-result');
            const resultId = button.dataset.resultId;
            const card = button.closest('.ai-result-card');
            
            if (confirm('Are you sure you want to delete this result? This action cannot be undone.')) {
                button.disabled = true;
                button.innerHTML = '<svg class="ai-spinner" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M21 12a9 9 0 11-6.219-8.56"/></svg>Deleting...';
                
                fetch(ajaxurl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        action: 'ai_styled_delete_result',
                        result_id: resultId,
                        nonce: '<?php echo wp_create_nonce('ai_styled_admin'); ?>'
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        card.style.opacity = '0.5';
                        card.style.transform = 'scale(0.95)';
                        setTimeout(() => {
                            card.remove();
                            // Reload page if no more results on current page
                            if (document.querySelectorAll('.ai-result-card').length === 0) {
                                location.reload();
                            }
                        }, 300);
                    } else {
                        alert('Error: ' + (data.data || 'Failed to delete result'));
                        button.disabled = false;
                        button.innerHTML = '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><polyline points="3,6 5,6 21,6"/><path d="m19,6v14a2,2 0 0,1-2,2H7a2,2 0 0,1-2-2V6m3,0V4a2,2 0 0,1,2-2h4a2,2 0 0,1,2,2v2"/></svg>Delete';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Network error occurred');
                    button.disabled = false;
                    button.innerHTML = '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><polyline points="3,6 5,6 21,6"/><path d="m19,6v14a2,2 0 0,1-2,2H7a2,2 0 0,1-2-2V6m3,0V4a2,2 0 0,1,2-2h4a2,2 0 0,1,2,2v2"/></svg>Delete';
                });
            }
        }
    });
});
</script>

<style>
.ai-results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.ai-result-card {
    background: var(--ai-admin-surface);
    border-radius: var(--ai-admin-radius);
    box-shadow: var(--ai-admin-shadow);
    overflow: hidden;
    transition: var(--ai-admin-transition);
}

.ai-result-card:hover {
    box-shadow: var(--ai-admin-shadow-lg);
    transform: translateY(-2px);
}

.ai-result-image {
    position: relative;
    aspect-ratio: 16/9;
    overflow: hidden;
}

.ai-result-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.ai-result-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--ai-admin-transition);
}

.ai-result-card:hover .ai-result-overlay {
    opacity: 1;
}

.ai-result-info {
    padding: 15px;
}

.ai-result-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.ai-result-category {
    background: var(--ai-admin-primary);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    text-transform: uppercase;
}

.ai-result-details {
    display: flex;
    gap: 15px;
    font-size: 14px;
    color: var(--ai-admin-muted);
}

.ai-result-user,
.ai-result-date {
    display: flex;
    align-items: center;
    gap: 5px;
}

.ai-result-user svg,
.ai-result-date svg {
    width: 14px;
    height: 14px;
}

.ai-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin-top: 30px;
}

.ai-pagination-info {
    color: var(--ai-admin-muted);
    font-weight: 500;
}

.ai-empty-state {
    text-align: center;
    padding: 60px 20px;
    color: var(--ai-admin-muted);
}

.ai-empty-state svg {
    width: 64px;
    height: 64px;
    margin-bottom: 20px;
    opacity: 0.5;
}

.ai-empty-state h3 {
    margin-bottom: 10px;
    color: var(--ai-admin-text);
}

.ai-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}
</style>
