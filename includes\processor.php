<?php
/**
 * AI Image Processor - Handles all image processing and overlay management
 */

defined('ABSPATH') || exit;

final class AI_Image_Processor {
    private string $api_token;
    private string $model_endpoint;
    private string $base_url = 'https://api.replicate.com/v1';
    private int $max_file_size;
    private array $allowed_formats;

    // OpenRouter API properties
    private string $openrouter_api_key;
    private string $openrouter_base_url = 'https://openrouter.ai/api/v1';
    
    public function __construct() {
        $this->api_token = get_option('ai_styled_api_token', '');
        $this->model_endpoint = get_option('ai_styled_model_endpoint', 'flux-kontext-apps/multi-image-kontext-pro');
        $this->max_file_size = get_option('ai_styled_max_file_size', 10485760);
        $this->allowed_formats = get_option('ai_styled_allowed_formats', ['jpg', 'jpeg', 'png', 'webp']);
        $this->openrouter_api_key = get_option('ai_styled_openrouter_api_key', '');
    }
    
    /**
     * Process user image with AI overlay
     */
    public function process(array $user_image, int $overlay_id, string $custom_prompt = ''): array {
        // Validate inputs
        if (!$this->validate_image($user_image)) {
            return $this->error('Invalid image file. Please check format and size.');
        }
        
        if (!$this->check_rate_limit()) {
            return $this->error('Rate limit exceeded. Please try again later.');
        }
        
        $overlay = $this->get_overlay($overlay_id);
        if (!$overlay) {
            return $this->error('Overlay not found.');
        }
        
        // If no API token, return test mode
        if (empty($this->api_token)) {
            return $this->test_mode_response($user_image, $overlay);
        }
        
        try {
            // Upload user image temporarily
            $user_image_url = $this->upload_temp_image($user_image);
            if (!$user_image_url) {
                return $this->error('Failed to upload image.');
            }
            
            // Generate prompt - try custom AI analysis first, fallback to default
            $prompt = '';
            if (empty($custom_prompt)) {
                $prompt = $this->generate_custom_prompt($user_image_url, $overlay->image_url);
            }

            // Fallback to existing prompt generation if custom prompt failed or custom_prompt provided
            if (empty($prompt)) {
                $prompt = $this->generate_prompt($overlay, $custom_prompt);
            }
            
            // Create prediction
            $prediction = $this->create_prediction($user_image_url, $overlay->image_url, $prompt);
            if (!$prediction) {
                $this->cleanup_temp_file($user_image_url);
                return $this->error('Failed to start AI processing.');
            }
            
            // Wait for completion
            $result = $this->wait_for_completion($prediction['id'], $overlay_id);
            $this->cleanup_temp_file($user_image_url);
            
            if ($result['success']) {
                $this->update_usage_count($overlay_id);
                return $this->success($result['data']);
            }
            
            return $this->error($result['message']);
            
        } catch (Exception $e) {
            error_log('AI Image Processor Error: ' . $e->getMessage());
            return $this->error('Processing failed. Please try again.');
        }
    }
    
    /**
     * Upload overlay image
     */
    public function upload_overlay(?array $image_file, array $form_data): array {
        if (!$image_file || !$this->validate_overlay_image($image_file)) {
            return $this->error('Invalid overlay image. Please upload a PNG file.');
        }
        
        // Process upload
        $upload_result = $this->process_overlay_upload($image_file);
        if (!$upload_result['success']) {
            return $upload_result;
        }
        
        // Save to database
        $overlay_data = [
            'title' => sanitize_text_field($form_data['title'] ?? ''),
            'description' => sanitize_textarea_field($form_data['description'] ?? ''),
            'image_url' => $upload_result['url'],
            'category' => sanitize_text_field($form_data['category'] ?? 'general'),
            'prompt_template' => sanitize_textarea_field($form_data['prompt_template'] ?? ''),
            'created_at' => current_time('mysql')
        ];
        
        global $wpdb;
        $result = $wpdb->insert("{$wpdb->prefix}ai_overlays", $overlay_data);
        
        if ($result === false) {
            // Clean up uploaded file on database error
            $this->delete_uploaded_file($upload_result['url']);
            return $this->error('Failed to save overlay to database.');
        }
        
        return $this->success([
            'message' => 'Overlay uploaded successfully!',
            'overlay_id' => $wpdb->insert_id
        ]);
    }
    
    /**
     * Delete overlay
     */
    public function delete_overlay(int $overlay_id): array {
        if ($overlay_id <= 0) {
            return $this->error('Invalid overlay ID.');
        }
        
        global $wpdb;
        
        // Get overlay data before deletion
        $overlay = $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM {$wpdb->prefix}ai_overlays WHERE id = %d", $overlay_id)
        );
        
        if (!$overlay) {
            return $this->error('Overlay not found.');
        }
        
        // Delete from database
        $deleted = $wpdb->delete(
            "{$wpdb->prefix}ai_overlays",
            ['id' => $overlay_id],
            ['%d']
        );
        
        if ($deleted === false) {
            return $this->error('Failed to delete overlay.');
        }
        
        // Clean up file
        $this->delete_uploaded_file($overlay->image_url);
        
        return $this->success(['message' => 'Overlay deleted successfully!']);
    }
    
    /**
     * Get all overlays
     */
    public function get_overlays(): array {
        global $wpdb;
        return $wpdb->get_results(
            "SELECT * FROM {$wpdb->prefix}ai_overlays ORDER BY usage_count DESC, created_at DESC"
        );
    }
    
    /**
     * Get single overlay
     */
    private function get_overlay(int $overlay_id): ?object {
        global $wpdb;
        return $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM {$wpdb->prefix}ai_overlays WHERE id = %d", $overlay_id)
        );
    }
    
    /**
     * Validate user image
     */
    private function validate_image(array $image): bool {
        if (!isset($image['tmp_name']) || !is_uploaded_file($image['tmp_name'])) {
            return false;
        }
        
        $file_info = wp_check_filetype_and_ext($image['tmp_name'], $image['name']);
        $file_type = strtolower(pathinfo($image['name'], PATHINFO_EXTENSION));
        
        if (!in_array($file_type, $this->allowed_formats)) {
            return false;
        }
        
        if ($image['size'] > $this->max_file_size) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Validate overlay image
     */
    private function validate_overlay_image(array $image): bool {
        if (!isset($image['tmp_name']) || !is_uploaded_file($image['tmp_name'])) {
            return false;
        }
        
        $file_info = wp_check_filetype_and_ext($image['tmp_name'], $image['name']);
        
        // Allow PNG for overlays (transparency support)
        if ($file_info['type'] !== 'image/png') {
            return false;
        }
        
        if ($image['size'] > $this->max_file_size) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Check rate limiting
     */
    private function check_rate_limit(): bool {
        $rate_limit = get_option('ai_styled_rate_limit', 50);
        $user_ip = $_SERVER['REMOTE_ADDR'] ?? '';
        $transient_key = 'ai_styled_rate_' . md5($user_ip);
        
        $requests = get_transient($transient_key) ?: 0;
        
        if ($requests >= $rate_limit) {
            return false;
        }
        
        set_transient($transient_key, $requests + 1, HOUR_IN_SECONDS);
        return true;
    }

    /**
     * Check OpenRouter API rate limiting
     */
    private function check_openrouter_rate_limit(): bool {
        $rate_limit = 20; // OpenRouter rate limit per hour
        $user_ip = $_SERVER['REMOTE_ADDR'] ?? '';
        $transient_key = 'ai_styled_openrouter_rate_' . md5($user_ip);

        $requests = get_transient($transient_key) ?: 0;

        if ($requests >= $rate_limit) {
            return false;
        }

        set_transient($transient_key, $requests + 1, HOUR_IN_SECONDS);
        return true;
    }

    /**
     * Upload temporary image
     */
    private function upload_temp_image(array $image): ?string {
        $upload_dir = wp_upload_dir();
        $temp_dir = $upload_dir['basedir'] . '/ai-temp/';
        
        if (!wp_mkdir_p($temp_dir)) {
            return null;
        }
        
        $filename = 'temp_' . uniqid() . '_' . sanitize_file_name($image['name']);
        $temp_path = $temp_dir . $filename;
        
        if (move_uploaded_file($image['tmp_name'], $temp_path)) {
            return $upload_dir['baseurl'] . '/ai-temp/' . $filename;
        }
        
        return null;
    }
    
    /**
     * Process overlay upload
     */
    private function process_overlay_upload(array $image): array {
        $upload_dir = wp_upload_dir();
        $ai_dir = $upload_dir['basedir'] . '/ai-overlays/';
        
        if (!wp_mkdir_p($ai_dir)) {
            return $this->error('Failed to create upload directory.');
        }
        
        $filename = 'overlay_' . uniqid() . '_' . sanitize_file_name($image['name']);
        $file_path = $ai_dir . $filename;
        
        if (move_uploaded_file($image['tmp_name'], $file_path)) {
            return [
                'success' => true,
                'url' => $upload_dir['baseurl'] . '/ai-overlays/' . $filename,
                'path' => $file_path
            ];
        }
        
        return $this->error('Failed to upload file.');
    }
    
    /**
     * Generate AI prompt
     */
    private function generate_prompt(object $overlay, string $custom_prompt): string {
        if (!empty(trim($custom_prompt))) {
            return trim($custom_prompt);
        }
        
        if (!empty($overlay->prompt_template)) {
            return $overlay->prompt_template;
        }
        
        // Default prompt based on category
        $prompts = [
            'Glass Room' => 'Integrate a sleek, modern glass room into the existing garden environment in a way that looks architecturally intentional and visually seamless. Place the glass room in the left corner of the garden, adjacent to the side fence and facing the main patio, ensuring no disruption to the original layout. Keep all existing elements intact—such as the house structure, landscaping, fence, lawn, trees, and any garden furniture. The glass room should harmonize with the overall style of the house and garden, using realistic reflections that mirror nearby plants and structures. Match the lighting angle, brightness, and color tone precisely with the original photo. Ensure the structure casts natural, consistent shadows and feels proportionate to the surrounding space. The result should resemble a professionally planned extension that fits naturally into the existing scene.',
            'Veranda' => 'Seamlessly integrate the modern veranda structure into the existing garden scene without altering any original elements of the base image. Position the veranda attached to the back wall of the house, extending over the existing patio area, while preserving all existing features including: house architecture, fence design, lawn areas, existing plants, patio elements, and garden furniture. The veranda should appear as a natural extension that complements the space. Match the lighting conditions, shadow direction, and color temperature of the original garden photo. Ensure the veranda\'s glass and metal surfaces reflect the surrounding garden environment accurately. Maintain the exact proportions and design details of both the original garden layout and the veranda structure. The integration should look professionally planned and architecturally coherent.',
            'Shading System' => 'Seamlessly integrate a modern shading system into the existing garden scene without altering any original elements of the base image. Position the shading system over the seating area on the wooden deck near the right-side fence, while preserving all existing features including: house architecture, fence design, lawn areas, existing plants, patio elements, and garden furniture. The shading system should appear as a functional and stylish addition that enhances comfort and complements the outdoor space. Match the lighting conditions, shadow direction, and color temperature of the original garden photo. Ensure the shading system casts realistic shadows and interacts naturally with surrounding structures. Maintain the exact proportions and visual coherence between the shading system and the garden layout. The integration should look professionally installed and seamlessly blended.'
        ];
        
        return $prompts[$overlay->category] ?? $prompts['Glass Room'];
    }

    /**
     * Generate custom prompt using OpenRouter AI vision analysis
     */
    private function generate_custom_prompt(string $user_image_url, string $overlay_image_url): string {
        // Check if OpenRouter API key is configured
        if (empty($this->openrouter_api_key)) {
            error_log('OpenRouter API key not configured, falling back to default prompt generation');
            return '';
        }

        // Check OpenRouter rate limit
        if (!$this->check_openrouter_rate_limit()) {
            error_log('OpenRouter rate limit exceeded, falling back to default prompt generation');
            return '';
        }

        try {
            // Validate image URLs
            if (!filter_var($user_image_url, FILTER_VALIDATE_URL) || !filter_var($overlay_image_url, FILTER_VALIDATE_URL)) {
                error_log('Invalid image URLs provided to OpenRouter API');
                return '';
            }

            // Check if URLs are accessible
            $user_image_check = wp_remote_head($user_image_url, ['timeout' => 10]);
            $overlay_image_check = wp_remote_head($overlay_image_url, ['timeout' => 10]);

            if (is_wp_error($user_image_check) || is_wp_error($overlay_image_check)) {
                error_log('One or more image URLs are not accessible for OpenRouter analysis');
                return '';
            }

            $messages = [
                [
                    'role' => 'user',
                    'content' => [
                        [
                            'type' => 'text',
                            'text' => 'Analyze these two images: the first is a user\'s garden/outdoor space, and the second is an architectural overlay element (like a glass room, veranda, or shading system). Generate a detailed prompt for AI image generation that will seamlessly integrate the overlay element into the user\'s space. The prompt should specify exact positioning, lighting matching, shadow casting, proportions, and preservation of all existing elements in the user\'s image. Focus on architectural compatibility and visual realism. Keep the response under 400 words.'
                        ],
                        [
                            'type' => 'image_url',
                            'image_url' => [
                                'url' => $user_image_url,
                                'detail' => 'high'
                            ]
                        ],
                        [
                            'type' => 'image_url',
                            'image_url' => [
                                'url' => $overlay_image_url,
                                'detail' => 'high'
                            ]
                        ]
                    ]
                ]
            ];

            $data = [
                'model' => 'anthropic/claude-3.5-sonnet',
                'messages' => $messages,
                'max_tokens' => 600,
                'temperature' => 0.2,
                'top_p' => 0.9
            ];

            $start_time = microtime(true);
            $response = $this->make_openrouter_request('POST', '/chat/completions', $data);
            $end_time = microtime(true);

            error_log('OpenRouter API request took ' . round(($end_time - $start_time), 2) . ' seconds');

            if (!$response) {
                error_log('OpenRouter API request failed completely');
                return '';
            }

            // Validate response structure
            if (!isset($response['choices']) || !is_array($response['choices']) || empty($response['choices'])) {
                error_log('OpenRouter API response missing choices array');
                return '';
            }

            if (!isset($response['choices'][0]['message']['content'])) {
                error_log('OpenRouter API response missing message content');
                return '';
            }

            $custom_prompt = trim($response['choices'][0]['message']['content']);

            // Validate prompt content
            if (empty($custom_prompt) || strlen($custom_prompt) < 50) {
                error_log('OpenRouter generated prompt is too short or empty');
                return '';
            }

            if (strlen($custom_prompt) > 2000) {
                error_log('OpenRouter generated prompt is too long, truncating');
                $custom_prompt = substr($custom_prompt, 0, 2000);
            }

            error_log('OpenRouter successfully generated custom prompt: ' . substr($custom_prompt, 0, 100) . '...');
            return $custom_prompt;

        } catch (Exception $e) {
            error_log('OpenRouter API exception: ' . $e->getMessage());
            error_log('Exception trace: ' . $e->getTraceAsString());
            return '';
        }
    }

    /**
     * Make OpenRouter API request with enhanced error handling
     */
    private function make_openrouter_request(string $method, string $endpoint, array $data = []): ?array {
        // Validate API key format
        if (!preg_match('/^sk-or-/', $this->openrouter_api_key)) {
            error_log('Invalid OpenRouter API key format');
            return null;
        }

        $url = $this->openrouter_base_url . $endpoint;

        $headers = [
            'Authorization' => 'Bearer ' . $this->openrouter_api_key,
            'Content-Type' => 'application/json',
            'HTTP-Referer' => home_url(),
            'X-Title' => 'AI Styled Image WordPress Plugin',
            'User-Agent' => 'AI-Styled-Image-Plugin/3.0.0'
        ];

        // Validate JSON encoding for POST requests
        $body = null;
        if ($method === 'POST') {
            $body = json_encode($data);
            if (json_last_error() !== JSON_ERROR_NONE) {
                error_log('OpenRouter API request JSON encode error: ' . json_last_error_msg());
                return null;
            }
        }

        $args = [
            'method' => $method,
            'headers' => $headers,
            'timeout' => 90, // Increased timeout for vision models
            'body' => $body,
            'sslverify' => true,
            'redirection' => 0
        ];

        $response = wp_remote_request($url, $args);

        if (is_wp_error($response)) {
            $error_message = $response->get_error_message();
            error_log('OpenRouter API request error: ' . $error_message);

            // Check for specific error types
            if (strpos($error_message, 'timeout') !== false) {
                error_log('OpenRouter API timeout - consider increasing timeout or trying again later');
            } elseif (strpos($error_message, 'SSL') !== false) {
                error_log('OpenRouter API SSL error - check SSL configuration');
            }

            return null;
        }

        $code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);

        // Enhanced error handling for different HTTP status codes
        if ($code !== 200) {
            $error_context = "OpenRouter API Error {$code}";

            switch ($code) {
                case 401:
                    error_log("{$error_context}: Invalid API key");
                    break;
                case 403:
                    error_log("{$error_context}: Access forbidden - check API key permissions");
                    break;
                case 429:
                    error_log("{$error_context}: Rate limit exceeded");
                    break;
                case 500:
                    error_log("{$error_context}: OpenRouter server error");
                    break;
                case 503:
                    error_log("{$error_context}: OpenRouter service unavailable");
                    break;
                default:
                    error_log("{$error_context}: {$body}");
            }

            return null;
        }

        if (empty($body)) {
            error_log('OpenRouter API returned empty response');
            return null;
        }

        $decoded = json_decode($body, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log('OpenRouter API response JSON decode error: ' . json_last_error_msg());
            error_log('Raw response body: ' . substr($body, 0, 500));
            return null;
        }

        // Validate response structure
        if (!is_array($decoded)) {
            error_log('OpenRouter API response is not a valid array');
            return null;
        }

        return $decoded;
    }

    /**
     * Create Replicate prediction
     */
    private function create_prediction(string $user_image_url, string $overlay_url, string $prompt): ?array {
        $data = [
            'input' => [
                'prompt' => $prompt,
                'aspect_ratio' => 'match_input_image',
                'input_image_1' => $overlay_url,
                'input_image_2' => $user_image_url,
                'output_format' => 'png',
                'safety_tolerance' => 2
            ]
        ];
        
        $response = $this->make_api_request('POST', "/models/{$this->model_endpoint}/predictions", $data);
        return $response && isset($response['id']) ? $response : null;
    }
    
    /**
     * Wait for prediction completion
     */
    private function wait_for_completion(string $prediction_id, int $overlay_id, int $max_wait = 300): array {
        $start_time = time();
        
        while (time() - $start_time < $max_wait) {
            $status = $this->make_api_request('GET', "/predictions/{$prediction_id}");
            
            if (!$status) {
                return $this->error('Failed to check processing status.');
            }
            
            if ($status['status'] === 'succeeded') {
                $image_url = is_array($status['output']) ? $status['output'][0] : $status['output'];
                $result_data = $this->save_result_image($image_url, $overlay_id);
                
                if ($result_data) {
                    return [
                        'success' => true,
                        'data' => [
                            'image_url' => $result_data['image_url'],
                            'result_id' => $result_data['result_id'],
                            'processing_time' => time() - $start_time
                        ]
                    ];
                }
                
                return $this->error('Failed to save result image.');
            }
            
            if ($status['status'] === 'failed') {
                return $this->error($status['error'] ?? 'AI processing failed.');
            }
            
            sleep(2);
        }
        
        return $this->error('Processing timeout. Please try again.');
    }
    
    /**
     * Make API request
     */
    private function make_api_request(string $method, string $endpoint, ?array $data = null): ?array {
        $url = $this->base_url . $endpoint;
        
        $args = [
            'method' => $method,
            'headers' => [
                'Authorization' => 'Bearer ' . $this->api_token,
                'Content-Type' => 'application/json',
                'Prefer' => 'wait'
            ],
            'timeout' => 30
        ];
        
        if ($data && $method !== 'GET') {
            $args['body'] = wp_json_encode($data);
        }
        
        $response = wp_remote_request($url, $args);
        
        if (is_wp_error($response)) {
            error_log('API Request Error: ' . $response->get_error_message());
            return null;
        }
        
        $code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);
        
        if ($code >= 200 && $code < 300) {
            return json_decode($body, true);
        }
        
        error_log("API Error {$code}: {$body}");
        return null;
    }
    
    /**
     * Save result image to custom directory and database
     */
    private function save_result_image(string $image_url, int $overlay_id): ?array {
        $response = wp_remote_get($image_url, ['timeout' => 60]);

        if (is_wp_error($response)) {
            error_log('Failed to download result image: ' . $response->get_error_message());
            return null;
        }

        $image_data = wp_remote_retrieve_body($response);
        $upload_dir = wp_upload_dir();

        // Create custom directory structure
        $results_dir = $upload_dir['basedir'] . '/ai-styled-image/results';
        if (!wp_mkdir_p($results_dir)) {
            error_log('Failed to create results directory: ' . $results_dir);
            return null;
        }

        // Generate filename: result-{user_id}-{timestamp}-{overlay_id}.png
        $user_id = get_current_user_id();
        $timestamp = time();
        $filename = "result-{$user_id}-{$timestamp}-{$overlay_id}.png";
        $file_path = $results_dir . '/' . $filename;
        $relative_path = '/ai-styled-image/results/' . $filename;

        if (!file_put_contents($file_path, $image_data)) {
            error_log('Failed to save result image to: ' . $file_path);
            return null;
        }

        // Store metadata in database
        global $wpdb;
        $result = $wpdb->insert(
            "{$wpdb->prefix}ai_results",
            [
                'user_id' => $user_id,
                'overlay_id' => $overlay_id,
                'image_path' => $relative_path,
                'created_at' => current_time('mysql')
            ],
            ['%d', '%d', '%s', '%s']
        );

        if ($result === false) {
            error_log('Failed to save result metadata to database');
            // Clean up the file if database insert failed
            unlink($file_path);
            return null;
        }

        $result_id = $wpdb->insert_id;
        $image_url = $upload_dir['baseurl'] . $relative_path;

        return [
            'result_id' => $result_id,
            'image_url' => $image_url,
            'file_path' => $file_path
        ];
    }
    
    /**
     * Test mode response (when no API token)
     */
    private function test_mode_response(array $user_image, object $overlay): array {
        $upload_dir = wp_upload_dir();
        $filename = 'test-' . uniqid() . '-' . sanitize_file_name($user_image['name']);
        $file_path = $upload_dir['path'] . '/' . $filename;
        
        if (move_uploaded_file($user_image['tmp_name'], $file_path)) {
            $attachment = [
                'guid' => $upload_dir['url'] . '/' . $filename,
                'post_mime_type' => $user_image['type'],
                'post_title' => sanitize_file_name($filename),
                'post_content' => '',
                'post_status' => 'inherit'
            ];
            
            $attachment_id = wp_insert_attachment($attachment, $file_path);
            
            if (!is_wp_error($attachment_id)) {
                require_once(ABSPATH . 'wp-admin/includes/image.php');
                $attachment_data = wp_generate_attachment_metadata($attachment_id, $file_path);
                wp_update_attachment_metadata($attachment_id, $attachment_data);
                
                $this->update_usage_count($overlay->id);
                
                return $this->success([
                    'image_url' => wp_get_attachment_url($attachment_id),
                    'attachment_id' => $attachment_id,
                    'test_mode' => true,
                    'message' => 'Test mode: Original image returned. Configure API token for AI processing.'
                ]);
            }
        }
        
        return $this->error('Failed to process image in test mode.');
    }
    
    /**
     * Update overlay usage count
     */
    private function update_usage_count(int $overlay_id): void {
        global $wpdb;
        $wpdb->query(
            $wpdb->prepare(
                "UPDATE {$wpdb->prefix}ai_overlays SET usage_count = usage_count + 1 WHERE id = %d",
                $overlay_id
            )
        );
    }
    
    /**
     * Clean up temporary file
     */
    private function cleanup_temp_file(string $url): void {
        if (strpos($url, '/ai-temp/') !== false) {
            $upload_dir = wp_upload_dir();
            $file_path = str_replace($upload_dir['baseurl'], $upload_dir['basedir'], $url);
            
            if (file_exists($file_path)) {
                wp_delete_file($file_path);
            }
        }
    }
    
    /**
     * Delete uploaded file
     */
    private function delete_uploaded_file(string $url): void {
        if (strpos($url, '/ai-overlays/') !== false) {
            $upload_dir = wp_upload_dir();
            $file_path = str_replace($upload_dir['baseurl'], $upload_dir['basedir'], $url);
            
            if (file_exists($file_path)) {
                wp_delete_file($file_path);
            }
        }
    }
    
    /**
     * Handle AJAX overlay upload
     */
    public function ajax_upload_overlay(): void {
        check_ajax_referer('ai_styled_admin', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized access', 403);
        }
        
        $edit_id = intval($_POST['edit_id'] ?? 0);
        $media_id = intval($_POST['media_id'] ?? 0);
        
        // Handle edit mode
        if ($edit_id > 0) {
            $result = $this->update_overlay($edit_id, $_POST);
            wp_send_json($result);
            return;
        }
        
        // Handle media library upload
        if ($media_id > 0) {
            $result = $this->create_overlay_from_media($media_id, $_POST);
            wp_send_json($result);
            return;
        }
        
        // Handle direct file upload
        if (isset($_FILES['overlay_image'])) {
            $result = $this->upload_overlay($_FILES['overlay_image'], $_POST);
            wp_send_json($result);
            return;
        }
        
        wp_send_json_error('No image provided');
    }
    
    /**
     * Handle AJAX overlay deletion
     */
    public function ajax_delete_overlay(): void {
        check_ajax_referer('ai_styled_admin', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized access', 403);
        }
        
        $overlay_id = intval($_POST['id'] ?? 0);
        $result = $this->delete_overlay($overlay_id);
        
        wp_send_json($result);
    }
    
    /**
     * Handle AJAX get overlay data
     */
    public function ajax_get_overlay(): void {
        check_ajax_referer('ai_styled_admin', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized access', 403);
        }
        
        $overlay_id = intval($_POST['id'] ?? 0);
        $overlay = $this->get_overlay($overlay_id);
        
        if ($overlay) {
            wp_send_json_success($overlay);
        } else {
            wp_send_json_error('Overlay not found');
        }
    }
    
    /**
     * Handle AJAX get overlays list
     */
    public function ajax_get_overlays(): void {
        check_ajax_referer('ai_styled_admin', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized access', 403);
        }
        
        $overlays = $this->get_overlays();
        $html = $this->render_overlays_grid($overlays);
        
        wp_send_json_success($html);
    }
    
    /**
     * Handle AJAX image processing
     */
    public function ajax_process_image(): void {
        check_ajax_referer('ai_styled_frontend', 'nonce');
        
        if (!isset($_FILES['user_image']) || !isset($_POST['overlay_id'])) {
            wp_send_json_error('Missing required data');
        }
        
        $overlay_id = intval($_POST['overlay_id']);
        $custom_prompt = sanitize_textarea_field($_POST['custom_prompt'] ?? '');
        
        $result = $this->process($_FILES['user_image'], $overlay_id, $custom_prompt);
        
        if ($result['success']) {
            wp_send_json_success($result['data']);
        } else {
            wp_send_json_error($result['message']);
        }
    }
    
    /**
     * Create overlay from media library
     */
    private function create_overlay_from_media(int $media_id, array $form_data): array {
        $attachment = get_post($media_id);
        
        if (!$attachment || $attachment->post_type !== 'attachment') {
            return $this->error('Invalid media attachment');
        }
        
        $file_path = get_attached_file($media_id);
        $mime_type = get_post_mime_type($media_id);
        
        if ($mime_type !== 'image/png') {
            return $this->error('Only PNG images are supported for overlays');
        }
        
        if (!file_exists($file_path)) {
            return $this->error('Media file not found');
        }
        
        // Copy to overlay directory
        $upload_dir = wp_upload_dir();
        $ai_dir = $upload_dir['basedir'] . '/ai-overlays/';
        
        if (!wp_mkdir_p($ai_dir)) {
            return $this->error('Failed to create upload directory');
        }
        
        $filename = 'overlay_' . uniqid() . '_' . basename($file_path);
        $new_path = $ai_dir . $filename;
        
        if (!copy($file_path, $new_path)) {
            return $this->error('Failed to copy file');
        }
        
        // Save to database
        $overlay_data = [
            'title' => sanitize_text_field($form_data['title'] ?? ''),
            'description' => sanitize_textarea_field($form_data['description'] ?? ''),
            'image_url' => $upload_dir['baseurl'] . '/ai-overlays/' . $filename,
            'category' => sanitize_text_field($form_data['category'] ?? 'general'),
            'prompt_template' => sanitize_textarea_field($form_data['prompt_template'] ?? ''),
            'created_at' => current_time('mysql')
        ];
        
        global $wpdb;
        $result = $wpdb->insert("{$wpdb->prefix}ai_overlays", $overlay_data);
        
        if ($result === false) {
            wp_delete_file($new_path);
            return $this->error('Failed to save overlay to database');
        }
        
        return $this->success([
            'message' => 'Overlay created successfully!',
            'overlay_id' => $wpdb->insert_id
        ]);
    }
    
    /**
     * Update existing overlay
     */
    private function update_overlay(int $overlay_id, array $form_data): array {
        global $wpdb;
        
        $overlay = $this->get_overlay($overlay_id);
        if (!$overlay) {
            return $this->error('Overlay not found');
        }
        
        $update_data = [
            'title' => sanitize_text_field($form_data['title'] ?? ''),
            'description' => sanitize_textarea_field($form_data['description'] ?? ''),
            'category' => sanitize_text_field($form_data['category'] ?? 'general'),
            'prompt_template' => sanitize_textarea_field($form_data['prompt_template'] ?? ''),
            'updated_at' => current_time('mysql')
        ];
        
        // Handle new image upload
        if (isset($_FILES['overlay_image']) && $_FILES['overlay_image']['size'] > 0) {
            $upload_result = $this->process_overlay_upload($_FILES['overlay_image']);
            if ($upload_result['success']) {
                // Delete old image
                $this->delete_uploaded_file($overlay->image_url);
                $update_data['image_url'] = $upload_result['url'];
            } else {
                return $upload_result;
            }
        }
        
        // Handle media library selection
        $media_id = intval($form_data['media_id'] ?? 0);
        if ($media_id > 0) {
            $attachment = get_post($media_id);
            if ($attachment && get_post_mime_type($media_id) === 'image/png') {
                $file_path = get_attached_file($media_id);
                $upload_dir = wp_upload_dir();
                $ai_dir = $upload_dir['basedir'] . '/ai-overlays/';
                
                wp_mkdir_p($ai_dir);
                
                $filename = 'overlay_' . uniqid() . '_' . basename($file_path);
                $new_path = $ai_dir . $filename;
                
                if (copy($file_path, $new_path)) {
                    $this->delete_uploaded_file($overlay->image_url);
                    $update_data['image_url'] = $upload_dir['baseurl'] . '/ai-overlays/' . $filename;
                }
            }
        }
        
        $result = $wpdb->update(
            "{$wpdb->prefix}ai_overlays",
            $update_data,
            ['id' => $overlay_id],
            ['%s', '%s', '%s', '%s', '%s'],
            ['%d']
        );
        
        if ($result === false) {
            return $this->error('Failed to update overlay');
        }
        
        return $this->success([
            'message' => 'Overlay updated successfully!',
            'overlay_id' => $overlay_id
        ]);
    }
    
    /**
     * Render overlays grid HTML
     */
    private function render_overlays_grid(array $overlays): string {
        if (empty($overlays)) {
            return '
                <div class="ai-empty-state">
                    <div class="ai-empty-icon">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                            <circle cx="8.5" cy="8.5" r="1.5"/>
                            <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/>
                        </svg>
                    </div>
                    <h3>No overlays yet</h3>
                    <p>Upload your first architectural overlay to get started</p>
                    <button type="button" class="ai-btn ai-btn-primary ai-empty-action">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M12 5v14M5 12h14"/>
                        </svg>
                        Add Your First Overlay
                    </button>
                </div>
            ';
        }
        
        $html = '';
        foreach ($overlays as $overlay) {
            $html .= sprintf('
                <div class="ai-overlay-card" data-id="%d">
                    <div class="ai-overlay-image">
                        <img src="%s" alt="%s">
                        <div class="ai-overlay-actions">
                            <button type="button" class="ai-action-btn ai-edit-overlay" data-id="%d" title="Edit">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                                    <path d="m18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
                                </svg>
                            </button>
                            <button type="button" class="ai-action-btn ai-delete-overlay" data-id="%d" title="Delete">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polyline points="3,6 5,6 21,6"/>
                                    <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div class="ai-overlay-info">
                        <h3>%s</h3>
                        <p class="ai-overlay-description">%s</p>
                        <div class="ai-overlay-meta">
                            <span class="ai-category">%s</span>
                            <span class="ai-usage">Used %d times</span>
                        </div>
                    </div>
                </div>
            ',
                esc_attr($overlay->id),
                esc_url($overlay->image_url),
                esc_attr($overlay->title),
                esc_attr($overlay->id),
                esc_attr($overlay->id),
                esc_html($overlay->title),
                esc_html($overlay->description),
                esc_html(ucfirst(str_replace('_', ' ', $overlay->category))),
                intval($overlay->usage_count)
            );
        }
        
        return $html;
    }
    
    /**
     * Get public overlay data for frontend
     */
    public function get_overlay_data(int $overlay_id): ?object {
        return $this->get_overlay($overlay_id);
    }
    
    /**
     * Success response helper
     */
    private function success(array $data = []): array {
        return [
            'success' => true,
            'data' => $data
        ];
    }
    
    /**
     * Error response helper
     */
    private function error(string $message): array {
        return [
            'success' => false,
            'message' => $message
        ];
    }
} 