# AI Styled Image Plugin - Technical Description

## Overview
The AI Styled Image plugin is a modern WordPress plugin that enables AI-powered architectural visualization by seamlessly integrating architectural elements into user-uploaded photos. The plugin uses the Replicate API with the flux-kontext-apps/multi-image-kontext-pro model to merge user images with predefined architectural overlays.

## Current Architecture (Version 3.0.0)

### Core Components

#### 1. Main Plugin File (`ai-styled-image.php`)
- **Class**: `AIStyledImagePlugin` (Singleton pattern)
- **Purpose**: Plugin initialization, hooks management, and core functionality
- **Key Features**:
  - WordPress hooks integration
  - Database table creation for overlays
  - Admin menu and shortcode registration
  - AJAX endpoint handling
  - Identity/branding system

#### 2. Image Processor (`includes/processor.php`)
- **Class**: `AI_Image_Processor`
- **Purpose**: Core AI processing logic and overlay management
- **Key Methods**:
  - `process()`: Main image processing with AI
  - `upload_overlay()`: Handle overlay uploads
  - `get_overlays()`: Retrieve available overlays
  - `create_prediction()`: Replicate API integration
  - `wait_for_completion()`: Async processing handling

#### 3. Admin Interface (`includes/admin.php`)
- **Purpose**: Single-page admin dashboard
- **Features**:
  - Overlay library management
  - API configuration
  - Brand identity customization
  - Usage statistics display

#### 4. Frontend Interface (`includes/frontend.php`)
- **Purpose**: User-facing image tool
- **Features**:
  - Drag & drop image upload
  - Overlay selection interface
  - Processing status display
  - Results download functionality

### Database Schema

#### `wp_ai_overlays` Table
```sql
CREATE TABLE wp_ai_overlays (
    id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    title varchar(255) NOT NULL,
    description text,
    image_url varchar(500) NOT NULL,
    category varchar(100) DEFAULT 'general',
    prompt_template text,
    usage_count int(11) DEFAULT 0,
    created_at timestamp DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY category (category),
    KEY usage_count (usage_count)
);
```

### Current Workflow

1. **User Upload**: User uploads base image via drag & drop interface
2. **Overlay Selection**: User selects architectural overlay from categorized grid
3. **Single-Step Processing**: 
   - User image and overlay sent directly to AI model
   - Predefined prompt template used based on overlay category
   - AI generates merged image in one step
4. **Result Display**: Generated image displayed with download option

### API Integration

#### Replicate API Configuration
- **Endpoint**: `https://api.replicate.com/v1/models/flux-kontext-apps/multi-image-kontext-pro/predictions`
- **Method**: POST with JSON payload
- **Input Parameters**:
  - `prompt`: Text description for merging
  - `input_image_1`: Overlay image URL
  - `input_image_2`: User image URL
  - `aspect_ratio`: "match_input_image"
  - `output_format`: "png"
  - `safety_tolerance`: 2

### Current Categories
- **Glass Room**: Modern conservatories and glass extensions
- **Veranda**: Contemporary veranda structures
- **Shading System**: Modern shading solutions

## Planned Enhancement: Multi-Step Processing

### New Workflow Architecture

#### Step 1: Image Analysis
- **Purpose**: AI analyzes both user image and selected overlay
- **Process**: Send both images to analysis model
- **Output**: Detailed prompt for optimal merging

#### Step 2: Prompt Generation
- **Purpose**: Create optimized merging instructions
- **Input**: Analysis results from Step 1
- **Output**: Custom prompt tailored to specific images

#### Step 3: Image Generation
- **Purpose**: Generate multiple variations
- **Input**: Custom prompt + original images
- **Output**: Multiple merged images based on "number of merges" setting

### Technical Implementation Plan

#### New Database Fields
```sql
ALTER TABLE wp_ai_overlays ADD COLUMN analysis_prompt text;
ALTER TABLE wp_ai_overlays ADD COLUMN merge_variations int DEFAULT 3;
```

#### New Processing Methods
```php
class AI_Image_Processor {
    // New methods for multi-step processing
    public function analyze_images($user_image, $overlay_image);
    public function generate_custom_prompt($analysis_result);
    public function generate_multiple_variations($prompt, $count);
}
```

#### Enhanced Frontend Interface
- **Step Indicator**: Visual progress through 3 steps
- **Analysis Preview**: Show AI analysis results
- **Variation Count**: User selects number of outputs (1-5)
- **Results Gallery**: Display multiple generated variations

### Configuration Options

#### Admin Settings
- **Analysis Model**: Configurable AI model for image analysis
- **Generation Model**: Configurable AI model for image generation
- **Max Variations**: Maximum number of variations per request
- **Analysis Timeout**: Timeout for analysis step
- **Generation Timeout**: Timeout for generation step

#### User Options
- **Variation Count**: Number of images to generate (1-5)
- **Style Intensity**: How strongly to apply the overlay
- **Merge Position**: Preferred placement of architectural element

### Performance Considerations

#### Caching Strategy
- **Analysis Results**: Cache analysis for same image pairs
- **Prompt Templates**: Cache generated prompts for reuse
- **Temporary Files**: Efficient cleanup of processing files

#### Rate Limiting
- **Multi-step Limits**: Adjusted rate limits for longer processing
- **Queue System**: Background processing for multiple variations
- **Progress Tracking**: Real-time status updates for users

### Security Enhancements

#### Input Validation
- **Image Analysis**: Validate analysis results before prompt generation
- **Prompt Sanitization**: Clean generated prompts for safety
- **Variation Limits**: Enforce reasonable variation counts

#### API Security
- **Token Rotation**: Support for API token rotation
- **Request Signing**: Enhanced API request security
- **Error Handling**: Improved error messages and logging

### User Experience Improvements

#### Progressive Enhancement
- **Fallback Mode**: Single-step processing if multi-step fails
- **Offline Indicators**: Clear status when API unavailable
- **Mobile Optimization**: Enhanced mobile interface

#### Accessibility
- **Screen Reader Support**: Proper ARIA labels for all steps
- **Keyboard Navigation**: Full keyboard accessibility
- **High Contrast**: Support for high contrast themes

## File Structure

```
ai-styled-image/
├── ai-styled-image.php          # Main plugin file
├── includes/
│   ├── processor.php            # AI processing logic
│   ├── admin.php               # Admin dashboard
│   └── frontend.php            # User interface
├── assets/
│   ├── style.css               # Frontend styles
│   ├── script.js               # Frontend JavaScript
│   ├── admin.css               # Admin styles
│   └── admin.js                # Admin JavaScript
├── README.md                   # Documentation
└── PLUGIN_DESCRIPTION.md       # This file
```

## Dependencies

### WordPress Requirements
- **Version**: 5.0+
- **PHP**: 7.4+
- **Memory**: 128MB minimum
- **Storage**: 10MB for plugin files

### External APIs
- **Replicate API**: Primary AI processing service
- **WordPress Media Library**: Image storage and management
- **WordPress Transients**: Rate limiting and caching

### JavaScript Libraries
- **Native ES6+**: No external dependencies
- **Fetch API**: Modern HTTP requests
- **File API**: Drag & drop functionality

## Security Features

### Data Protection
- **Nonce Verification**: All AJAX requests secured
- **Input Sanitization**: All user data properly cleaned
- **File Validation**: Strict upload requirements
- **Temporary Storage**: Auto-cleanup of uploaded files

### Access Control
- **Capability Checks**: Proper WordPress permissions
- **API Token Security**: Secure storage and handling
- **Rate Limiting**: Prevents API abuse

## Performance Metrics

### Current Performance
- **Single Processing**: 30-60 seconds average
- **File Size Limit**: 10MB maximum
- **Concurrent Users**: 50 requests/hour per IP
- **Memory Usage**: ~64MB per request

### Expected Multi-Step Performance
- **Analysis Step**: 10-20 seconds
- **Prompt Generation**: 2-5 seconds
- **Image Generation**: 30-90 seconds (depending on variation count)
- **Total Time**: 45-120 seconds for full process

## Future Roadmap

### Phase 1: Multi-Step Implementation
- Implement 3-step processing workflow
- Add variation count selection
- Enhanced progress tracking

### Phase 2: Advanced Features
- Custom prompt editing
- Batch processing capabilities
- API provider alternatives

### Phase 3: Enterprise Features
- White-label customization
- Advanced analytics
- API rate optimization

## Support and Maintenance

### Error Handling
- **Graceful Degradation**: Fallback to single-step processing
- **Detailed Logging**: Comprehensive error tracking
- **User Feedback**: Clear error messages for users

### Monitoring
- **API Status**: Real-time API availability checking
- **Usage Analytics**: Track popular overlays and success rates
- **Performance Metrics**: Monitor processing times and failures

This plugin represents a modern approach to AI-powered image processing in WordPress, with a clear path for enhancement through multi-step processing that will provide users with more control and better results.